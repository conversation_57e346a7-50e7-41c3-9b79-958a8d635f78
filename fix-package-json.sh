#!/bin/bash

# Fix package.json for Wasp compatibility
# This script ensures Vite is at the correct version before <PERSON><PERSON> checks

echo "🔧 Fixing package.json for Wasp compatibility..."

# Show current package.json vite entry
echo "Current vite entry:"
grep -n '"vite"' package.json || echo "vite not found"

# Fix vite version to ^4.3.9 (required by <PERSON>p)
sed -i 's/"vite": "\^5\.[0-9]\+\.[0-9]\+"/"vite": "^4.3.9"/g' package.json
sed -i 's/"vite": "\^4\.[4-9]\.[0-9]\+"/"vite": "^4.3.9"/g' package.json
sed -i 's/"vite": "5\.[0-9]\+\.[0-9]\+"/"vite": "^4.3.9"/g' package.json

# Verify the fix
echo "Fixed vite entry:"
grep -n '"vite"' package.json || echo "vite not found"

echo "✅ Package.json fixed for Wasp compatibility"
