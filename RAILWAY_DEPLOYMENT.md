# 🚄 CareerDart Railway Deployment Guide

This guide will help you deploy your CareerDart application to Railway with PostgreSQL database.

## 🚀 Quick Deployment Steps

### 1. Install Railway CLI
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login to Railway
railway login
```

### 2. Create Railway Project
```bash
# Initialize Railway project
railway init

# Link to existing project (if you have one)
# railway link [project-id]
```

### 3. Set Up PostgreSQL Database
```bash
# Add PostgreSQL service to your project
railway add postgresql

# This will automatically create a DATABASE_URL environment variable
```

### 4. Configure Environment Variables
Set these environment variables in your Railway project dashboard:

**Required Variables:**
- `DATABASE_URL` - (Auto-generated by Railway PostgreSQL service)
- `OPENAI_API_KEY` - Your OpenAI API key
- `GOOGLE_CLIENT_ID` - Google OAuth client ID  
- `GOOGLE_CLIENT_SECRET` - Google OAuth client secret
- `WASP_SERVER_URL` - Will be auto-set by Railway

**Optional Variables:**
- `SENDGRID_API_KEY` - For email functionality
- `STRIPE_KEY` - For payment processing
- `PRODUCT_PRICE_ID` - Stripe product price ID
- `PRODUCT_CREDITS_PRICE_ID` - Stripe credits price ID

### 5. Deploy to Railway
```bash
# Deploy your application
railway up

# Or use the npm script
npm run railway:deploy
```

## 🔧 Configuration Files

The following files have been created for Railway deployment:

- **`railway.json`** - Railway service configuration
- **`nixpacks.toml`** - Build configuration for Nixpacks
- **`scripts/build-for-railway.sh`** - Custom build script

## 🌐 Environment Variables Setup

### Via Railway CLI:
```bash
# Set environment variables via CLI
railway variables set OPENAI_API_KEY=your_openai_key
railway variables set GOOGLE_CLIENT_ID=your_google_client_id
railway variables set GOOGLE_CLIENT_SECRET=your_google_client_secret
```

### Via Railway Dashboard:
1. Go to your Railway project dashboard
2. Click on your service
3. Go to "Variables" tab
4. Add your environment variables

## 🗄️ Database Setup

Railway will automatically:
- Create a PostgreSQL database
- Generate a `DATABASE_URL` environment variable
- Handle database connections

The build script will automatically run database migrations during deployment.

## 🔍 Monitoring & Logs

```bash
# View deployment logs
railway logs

# Monitor your application
railway status
```

## 🚨 Troubleshooting

### Build Issues
- Check Railway build logs: `railway logs --deployment`
- Verify all environment variables are set
- Ensure Wasp version compatibility

### Database Issues
- Verify DATABASE_URL is correctly set
- Check database connection in Railway dashboard
- Run migrations manually: `railway run wasp db migrate-deploy`

### Application Issues
- Check application logs: `railway logs`
- Verify all required environment variables
- Test locally first: `npm run railway:build`

## 📊 Railway Project Structure

```
Your Railway Project
├── CareerDart (Web Service)
│   ├── Environment Variables
│   ├── Build Logs
│   └── Deployment History
└── PostgreSQL (Database Service)
    ├── Connection Details
    └── Database Metrics
```

## 🎯 Next Steps After Deployment

1. **Custom Domain**: Add your custom domain in Railway dashboard
2. **SSL Certificate**: Railway provides automatic HTTPS
3. **Monitoring**: Set up monitoring and alerts
4. **Scaling**: Configure auto-scaling if needed

## 💡 Tips for Success

- **Test Locally**: Always test the build script locally first
- **Environment Variables**: Double-check all required variables are set
- **Database**: Let Railway handle PostgreSQL setup automatically
- **Logs**: Monitor deployment logs for any issues
- **Rollback**: Railway supports easy rollbacks if needed

## 🔗 Useful Commands

```bash
# Check project status
railway status

# View environment variables
railway variables

# Connect to database
railway connect postgresql

# Open project in browser
railway open

# Delete deployment
railway down
```

Happy deploying! 🚀
