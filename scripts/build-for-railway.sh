#!/bin/bash

# CareerDart Railway Build Script
# This script prepares the application for Railway deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "Starting Railway build process for CareerDart..."

# Debug information
print_status "Build Environment Information:"
echo "Current working directory: $(pwd)"
echo "Node version: $(node --version)"
echo "NPM version: $(npm --version)"
echo "Environment: ${NODE_ENV:-development}"
echo "Railway: ${RAILWAY_ENVIRONMENT:-local}"

# Check Node.js version requirement
NODE_VERSION=$(node --version | sed 's/v//')
REQUIRED_VERSION="20.0.0"
if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    print_error "Node.js version $NODE_VERSION does not meet Wasp's requirement of $REQUIRED_VERSION or higher"
    exit 1
else
    print_success "Node.js version $NODE_VERSION meets Wasp's requirements"
fi

# Check if we're in the right directory
if [ ! -f "main.wasp" ]; then
    print_error "main.wasp not found. Please run this script from the project root."
    echo "Directory contents:"
    ls -la
    exit 1
fi

print_status "Checking for Wasp installation..."

# Install Wasp if not already installed
if ! command -v wasp &> /dev/null; then
    print_warning "Wasp not found. Installing Wasp v0.15.0..."

    # Create local bin directory
    mkdir -p $HOME/.local/bin

    # Download and install Wasp version 0.15.0
    curl -sSL https://get.wasp.sh/installer.sh | sh -s -- -v 0.15.0

    # Add to PATH
    export PATH="$HOME/.local/bin:$PATH"

    # Verify installation
    if command -v wasp &> /dev/null; then
        print_success "Wasp installed successfully: $(wasp version)"
    else
        print_error "Wasp installation failed"
        exit 1
    fi
else
    print_success "Wasp found: $(wasp version)"
fi

# Install dependencies
print_status "Installing dependencies..."

# Force clean install to avoid caching issues
print_status "Clearing npm cache and node_modules..."
rm -rf node_modules package-lock.json
npm cache clean --force

# Ensure vite is in devDependencies with correct version (Wasp requirement)
print_status "Checking Vite dependency placement and version..."
if grep -q '"vite"' package.json | grep -q '"dependencies"'; then
    print_warning "Moving vite from dependencies to devDependencies..."
    npm uninstall vite
    npm install --save-dev vite@^4.3.9
fi

# Force install exact Vite version
print_status "Installing exact Vite version ^4.3.9 for Wasp compatibility..."
npm install --save-dev vite@^4.3.9

# Install all dependencies
npm install

# Verify Vite version after installation
VITE_VERSION=$(npm list vite --depth=0 2>/dev/null | grep vite@ | sed 's/.*vite@//' | sed 's/ .*//' || echo "not installed")
print_status "Final Vite version: $VITE_VERSION"

# Show package.json vite entry for debugging
print_status "Vite in package.json: $(grep -A1 -B1 '"vite"' package.json || echo 'not found')"

# Ensure TypeScript configuration meets Wasp requirements
print_status "Verifying TypeScript configuration..."
if [ -f "tsconfig.json" ]; then
    # Check if outDir is set correctly
    if grep -q '"outDir".*".wasp/phantom"' tsconfig.json; then
        print_warning "Fixing tsconfig.json outDir for Wasp compatibility..."
        sed -i 's|"outDir": ".wasp/phantom"|"outDir": ".wasp/out/user"|g' tsconfig.json
    fi
    print_status "TypeScript outDir: $(grep -o '"outDir": "[^"]*"' tsconfig.json || echo 'not found')"
fi

# Generate Prisma client
print_status "Generating Prisma client..."
wasp db generate

# Run database migrations if DATABASE_URL is set
if [ ! -z "$DATABASE_URL" ]; then
    print_status "Running database migrations..."
    if ! wasp db migrate-deploy; then
        print_warning "Database migration failed, continuing anyway..."
    else
        print_success "Database migrations completed"
    fi
else
    print_warning "DATABASE_URL not set, skipping migrations"
fi

# Build the application
print_status "Building Wasp application..."
if ! wasp build; then
    print_error "Wasp build failed"
    exit 1
fi

# Verify build output
if [ ! -d ".wasp/build" ]; then
    print_error "Build failed - .wasp/build directory not found"
    exit 1
fi

print_success "Railway build completed successfully!"
print_status "Build output available in: .wasp/build/"

# Show build summary
echo ""
echo "=== Build Summary ==="
echo "✅ Wasp installation: OK"
echo "✅ Dependencies: OK"
echo "✅ Prisma generation: OK"
echo "✅ Database migrations: $([ ! -z "$DATABASE_URL" ] && echo "OK" || echo "SKIPPED")"
echo "✅ Wasp build: OK"
echo ""
print_success "Ready for Railway deployment!"
