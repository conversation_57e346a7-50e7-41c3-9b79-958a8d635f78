#!/bin/bash

# CareerDart Railway Build Script
# This script prepares the application for Railway deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "Starting Railway build process for CareerDart..."

# Debug information
print_status "Build Environment Information:"
echo "Current working directory: $(pwd)"
echo "Node version: $(node --version)"
echo "NPM version: $(npm --version)"
echo "Environment: ${NODE_ENV:-development}"
echo "Railway: ${RAILWAY_ENVIRONMENT:-local}"

# Check if we're in the right directory
if [ ! -f "main.wasp" ]; then
    print_error "main.wasp not found. Please run this script from the project root."
    echo "Directory contents:"
    ls -la
    exit 1
fi

print_status "Checking for Wasp installation..."

# Install Wasp if not already installed
if ! command -v wasp &> /dev/null; then
    print_warning "Wasp not found. Installing Wasp v0.15.0..."
    
    # Create local bin directory
    mkdir -p $HOME/.local/bin
    
    # Download and install Wasp
    curl -sSL https://get.wasp.sh/installer.sh | VERSION=0.15.0 bash
    
    # Add to PATH
    export PATH="$HOME/.local/bin:$PATH"
    
    # Verify installation
    if command -v wasp &> /dev/null; then
        print_success "Wasp installed successfully: $(wasp version)"
    else
        print_error "Wasp installation failed"
        exit 1
    fi
else
    print_success "Wasp found: $(wasp version)"
fi

# Install dependencies
print_status "Installing dependencies..."
npm install

# Generate Prisma client
print_status "Generating Prisma client..."
wasp db generate

# Run database migrations if DATABASE_URL is set
if [ ! -z "$DATABASE_URL" ]; then
    print_status "Running database migrations..."
    if ! wasp db migrate-deploy; then
        print_warning "Database migration failed, continuing anyway..."
    else
        print_success "Database migrations completed"
    fi
else
    print_warning "DATABASE_URL not set, skipping migrations"
fi

# Build the application
print_status "Building Wasp application..."
if ! wasp build; then
    print_error "Wasp build failed"
    exit 1
fi

# Verify build output
if [ ! -d ".wasp/build" ]; then
    print_error "Build failed - .wasp/build directory not found"
    exit 1
fi

print_success "Railway build completed successfully!"
print_status "Build output available in: .wasp/build/"

# Show build summary
echo ""
echo "=== Build Summary ==="
echo "✅ Wasp installation: OK"
echo "✅ Dependencies: OK"
echo "✅ Prisma generation: OK"
echo "✅ Database migrations: $([ ! -z "$DATABASE_URL" ] && echo "OK" || echo "SKIPPED")"
echo "✅ Wasp build: OK"
echo ""
print_success "Ready for Railway deployment!"
