#!/bin/bash

# CareerDart Deployment Troubleshooting Script
# Handles common issues that may arise after TypeScript fixes

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "CareerDart Deployment Troubleshooting Script"
echo "=============================================="

# Check if we're in the right directory
if [ ! -f "main.wasp" ]; then
    print_error "main.wasp not found. Please run this script from the project root."
    exit 1
fi

# Function to fix Prisma client issues
fix_prisma_issues() {
    print_status "Fixing potential Prisma client issues..."
    
    # Regenerate Prisma client
    wasp db generate || print_warning "Could not regenerate Prisma client"
    
    # Check for Prisma runtime issues
    if [ -d "node_modules/@prisma" ]; then
        print_success "Prisma client found in node_modules"
    else
        print_warning "Prisma client not found, attempting to install..."
        npm install @prisma/client@5.19.1 || true
    fi
}

# Function to fix missing dependencies
fix_missing_dependencies() {
    print_status "Checking for missing dependencies..."
    
    # Install any missing critical dependencies
    npm install --save-dev \
        @types/node@18.0.0 \
        @types/express@4.17.13 \
        @types/cors@2.8.5 \
        typescript@5.1.0 \
        || print_warning "Could not install some dependencies"
    
    # Check for React types compatibility
    if npm list @types/react | grep -q "18.0.37"; then
        print_success "React types are correctly installed"
    else
        print_warning "React types may need fixing"
        npm install --save-dev @types/react@18.0.37 @types/react-dom@18.0.11 || true
    fi
}

# Function to fix build output issues
fix_build_output() {
    print_status "Checking build output structure..."
    
    # Ensure build directories exist
    mkdir -p .wasp/build/web-app/build
    mkdir -p .wasp/build/server
    
    # Check if index.html exists
    if [ -f ".wasp/build/web-app/build/index.html" ]; then
        print_success "Main HTML file found"
    else
        print_warning "Main HTML file missing - this may cause deployment issues"
    fi
    
    # Check build size
    if [ -d ".wasp/build" ]; then
        BUILD_SIZE=$(du -sh .wasp/build 2>/dev/null | cut -f1 || echo "Unknown")
        print_status "Build size: $BUILD_SIZE"
    fi
}

# Function to fix environment variable issues
fix_env_issues() {
    print_status "Checking environment configuration..."
    
    # Check for required environment files
    if [ -f ".env.server" ]; then
        print_success "Environment file found"
        
        # Check for database URL
        if grep -q "DATABASE_URL" .env.server; then
            print_success "Database URL configured"
        else
            print_warning "DATABASE_URL not found in .env.server"
        fi
    else
        print_warning "No .env.server file found"
    fi
}

# Function to create a minimal test build
create_test_build() {
    print_status "Creating minimal test build..."
    
    # Set minimal environment variables
    export NODE_ENV=production
    export SKIP_TYPE_CHECK=true
    export GENERATE_SOURCEMAP=false
    
    # Try a minimal build
    if wasp build; then
        print_success "Test build successful!"
        return 0
    else
        print_error "Test build failed"
        return 1
    fi
}

# Function to generate deployment report
generate_report() {
    print_status "Generating deployment report..."
    
    cat > deployment-report.txt << EOF
CareerDart Deployment Report
Generated: $(date)

Environment:
- Node version: $(node --version 2>/dev/null || echo "Not available")
- NPM version: $(npm --version 2>/dev/null || echo "Not available")
- Wasp version: $(wasp version 2>/dev/null || echo "Not available")

Project Structure:
- main.wasp: $([ -f "main.wasp" ] && echo "✅ Found" || echo "❌ Missing")
- package.json: $([ -f "package.json" ] && echo "✅ Found" || echo "❌ Missing")
- tsconfig.json: $([ -f "tsconfig.json" ] && echo "✅ Found" || echo "❌ Missing")
- .env.server: $([ -f ".env.server" ] && echo "✅ Found" || echo "❌ Missing")

Build Output:
- .wasp/build directory: $([ -d ".wasp/build" ] && echo "✅ Found" || echo "❌ Missing")
- Web app build: $([ -d ".wasp/build/web-app/build" ] && echo "✅ Found" || echo "❌ Missing")
- Server build: $([ -d ".wasp/build/server" ] && echo "✅ Found" || echo "❌ Missing")
- Main HTML: $([ -f ".wasp/build/web-app/build/index.html" ] && echo "✅ Found" || echo "❌ Missing")

Dependencies:
- node_modules: $([ -d "node_modules" ] && echo "✅ Found" || echo "❌ Missing")
- Prisma client: $([ -d "node_modules/@prisma/client" ] && echo "✅ Found" || echo "❌ Missing")
- React types: $(npm list @types/react 2>/dev/null | grep -o "@types/react@[^[:space:]]*" || echo "❌ Not found")

Recent Changes:
- Last commit: $(git log -1 --oneline 2>/dev/null || echo "Git not available")
- Modified files: $(git status --porcelain 2>/dev/null | wc -l || echo "Unknown") files

Recommendations:
$([ ! -f ".wasp/build/web-app/build/index.html" ] && echo "- Run 'wasp build' to generate build files")
$([ ! -d "node_modules/@prisma/client" ] && echo "- Run 'npm install @prisma/client' to fix Prisma issues")
$([ ! -f ".env.server" ] && echo "- Create .env.server with required environment variables")
EOF

    print_success "Deployment report saved to deployment-report.txt"
}

# Main execution
print_status "Starting troubleshooting process..."

# Run all fixes
fix_missing_dependencies
fix_prisma_issues
fix_env_issues
fix_build_output

# Try a test build
if create_test_build; then
    print_success "All checks passed! Deployment should work."
else
    print_warning "Some issues detected. Check the report for details."
fi

# Generate report
generate_report

print_status "Troubleshooting complete!"
print_status "Check deployment-report.txt for detailed information."
