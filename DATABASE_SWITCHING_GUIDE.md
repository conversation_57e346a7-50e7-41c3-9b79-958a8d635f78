# 🔄 Database Switching Guide - CareerDart

## ❌ **Problem: Login Page Fails with Production DB**

When switching from local to production database URL, the login page fails to load due to:

1. **Schema Mismatch**: Production DB may have different schema than local
2. **Prisma Client Out of Sync**: Client generated for wrong database
3. **Missing Migrations**: Production DB missing required tables
4. **SSL Connection Issues**: Production requires SSL, local doesn't

## ✅ **Solution: Safe Database Switching**

### **Quick Fix (Recommended)**
```bash
# Switch back to local database safely
./scripts/switch-database.sh local

# Or switch to production database safely
./scripts/switch-database.sh production

# Check current status
./scripts/switch-database.sh status
```

### **Manual Fix (If script doesn't work)**

#### **1. Switch to Local Database**
```bash
# Edit .env.server
DATABASE_URL=postgresql://postgres:postgres@localhost:5432/coverlettergpt
#DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require

# Regenerate Prisma client
wasp db generate

# Apply migrations to local database
wasp db migrate-dev

# Start the application
wasp start
```

#### **2. Switch to Production Database**
```bash
# Edit .env.server
#DATABASE_URL=postgresql://postgres:postgres@localhost:5432/coverlettergpt
DATABASE_URL=postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require

# Regenerate Prisma client
wasp db generate

# Apply migrations to production database (CAREFUL!)
wasp db migrate-deploy

# Start the application
wasp start
```

## 🔍 **Why This Happens**

### **Prisma Client Generation**
- Prisma client is generated based on the current DATABASE_URL
- When you switch databases, the client becomes out of sync
- Must regenerate client after switching

### **Database Schema**
- Local and production databases may have different schemas
- Missing tables/columns cause authentication failures
- Migrations ensure schema consistency

### **SSL Requirements**
- Production databases (Neon) require SSL connections
- Local PostgreSQL typically doesn't use SSL
- Connection string differences can cause issues

## 🛠 **Troubleshooting**

### **Login Page Still Fails**
```bash
# 1. Check database connection
wasp db generate

# 2. Verify migrations are applied
wasp db migrate-dev  # for local
wasp db migrate-deploy  # for production

# 3. Clear browser cache and restart
# 4. Check browser console for errors
```

### **Migration Errors**
```bash
# Reset local database (DESTRUCTIVE)
wasp db reset

# Or manually drop and recreate
dropdb coverlettergpt
createdb coverlettergpt
wasp db migrate-dev
```

### **Connection Refused**
```bash
# For local database, ensure PostgreSQL is running
brew services start postgresql
# or
sudo service postgresql start

# For production, check Neon dashboard
```

## 📋 **Best Practices**

### **Development Workflow**
1. **Use local database for development**
2. **Test with production database before deployment**
3. **Always backup before switching**
4. **Use the switching script for safety**

### **Environment Management**
```bash
# Keep separate environment files
.env.server          # Current active config
.env.server.local    # Local database config
.env.server.prod     # Production database config
```

### **Database Backup**
```bash
# Backup local database before switching
pg_dump coverlettergpt > backup_local.sql

# Backup production database (if needed)
# Use Neon dashboard or pg_dump with production URL
```

## 🚀 **Quick Commands**

```bash
# Check current database
./scripts/switch-database.sh status

# Switch to local (safe for development)
./scripts/switch-database.sh local

# Switch to production (for testing deployment)
./scripts/switch-database.sh production

# Start application
wasp start
```

## ⚠️ **Important Notes**

- **Always use local database for development**
- **Only use production database for deployment testing**
- **Never run destructive operations on production**
- **Keep backups of important data**
- **Test authentication after switching databases**
