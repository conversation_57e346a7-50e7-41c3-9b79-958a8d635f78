[phases.setup]
nixPkgs = ["nodejs-18_x", "npm-9_x", "curl", "bash", "postgresql"]

[phases.install]
cmds = [
  "npm ci --only=production",
  "curl -sSL https://get.wasp.sh/installer.sh | VERSION=0.15.0 bash"
]

[phases.build]
cmds = [
  "export PATH=\"$HOME/.local/bin:$PATH\"",
  "npm run railway:build"
]

[start]
cmd = "export PATH=\"$HOME/.local/bin:$PATH\" && wasp start"

[variables]
NODE_ENV = "production"
WASP_ENV = "production"
