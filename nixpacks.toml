[phases.setup]
nixPkgs = ["nodejs-20_x", "npm-10_x", "curl", "bash", "postgresql"]

[phases.install]
cmds = [
  "chmod +x fix-package-json.sh",
  "./fix-package-json.sh",
  "rm -rf node_modules package-lock.json",
  "npm cache clean --force",
  "npm install",
  "curl -sSL https://get.wasp.sh/installer.sh | sh -s -- -v 0.15.0"
]

[phases.build]
cmds = [
  "export PATH=\"$HOME/.local/bin:$PATH\"",
  "npm run railway:build"
]

[start]
cmd = "export PATH=\"$HOME/.local/bin:$PATH\" && wasp start"

[variables]
NODE_ENV = "production"
WASP_ENV = "production"
