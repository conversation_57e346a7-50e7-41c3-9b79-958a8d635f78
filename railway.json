{"$schema": "https://railway.app/railway.schema.json", "build": {"builder": "NIXPACKS", "buildCommand": "npm run railway:build", "watchPatterns": ["**/*.wasp", "src/**/*", "package.json", "schema.prisma"]}, "deploy": {"startCommand": "wasp start", "healthcheckPath": "/", "healthcheckTimeout": 300, "restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 3}, "environments": {"production": {"variables": {"NODE_ENV": "production", "WASP_ENV": "production"}}}}